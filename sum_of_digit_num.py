# Sum of digits using FOR LOOP only

num = int(input("Enter the number: "))

# Convert number to string to use for loop
num_str = str(num)
total = 0

# Use for loop to go through each digit
for digit_char in num_str:
    digit = int(digit_char)    # Convert character back to number
    total = total + digit      # Add digit to total

print("Sum of digits is:", total)

# Show the calculation step by step using for loop
calculation = ""
for i in range(len(num_str)):
    digit = int(num_str[i])

    if i == 0:
        calculation = str(digit)
    else:
        calculation = calculation + " + " + str(digit)

print(f"Calculation: {calculation} = {total}")

# Alternative method: Using for loop with range
print("\nAlternative method:")
num2 = int(input("Enter another number: "))
num_str2 = str(num2)
total2 = 0

for i in range(len(num_str2)):
    digit = int(num_str2[i])
    total2 = total2 + digit
    print(f"Step {i+1}: Adding {digit}, Total so far: {total2}")

print(f"Final sum: {total2}")