# Simple Calculator Program

def add(x, y):
    """Addition function"""
    return x + y

def subtract(x, y):
    """Subtraction function"""
    return x - y

def multiply(x, y):
    """Multiplication function"""
    return x * y

def divide(x, y):
    """Division function"""
    if y == 0:
        return "Error: Cannot divide by zero!"
    return x / y

def display_menu():
    """Display calculator menu"""
    print("\n=== Simple Calculator ===")
    print("Select operation:")
    print("1. Addition (+)")
    print("2. Subtraction (-)")
    print("3. Multiplication (*)")
    print("4. Division (/)")
    print("5. Exit")
    print("=" * 25)

def get_numbers():
    """Get two numbers from user"""
    try:
        num1 = float(input("Enter first number: "))
        num2 = float(input("Enter second number: "))
        return num1, num2
    except ValueError:
        print("Error: Please enter valid numbers!")
        return None, None

def main():
    """Main calculator function"""
    print("Welcome to Simple Calculator!")

    while True:
        display_menu()

        try:
            choice = input("Enter your choice (1-5): ")

            if choice == '5':
                print("Thank you for using the calculator!")
                break

            if choice in ['1', '2', '3', '4']:
                num1, num2 = get_numbers()

                if num1 is not None and num2 is not None:
                    if choice == '1':
                        result = add(num1, num2)
                        print(f"{num1} + {num2} = {result}")

                    elif choice == '2':
                        result = subtract(num1, num2)
                        print(f"{num1} - {num2} = {result}")

                    elif choice == '3':
                        result = multiply(num1, num2)
                        print(f"{num1} * {num2} = {result}")

                    elif choice == '4':
                        result = divide(num1, num2)
                        if isinstance(result, str):  # Error message
                            print(result)
                        else:
                            print(f"{num1} / {num2} = {result}")
            else:
                print("Invalid choice! Please select 1-5.")

        except KeyboardInterrupt:
            print("\n\nCalculator interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"An error occurred: {e}")

# Run the calculator
if __name__ == "__main__":
    main()